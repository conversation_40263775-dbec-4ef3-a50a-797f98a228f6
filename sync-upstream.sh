#!/bin/bash
# 同步 upstream 並更新依賴

# 顯示當前狀態
echo "🔍 檢查 git 狀態..."
git status --short

# 取得當前分支名稱
BRANCH=$(git branch --show-current)
echo "📌 當前分支: $BRANCH"

# Fetch upstream
echo "📥 Fetching upstream..."
git fetch upstream

# Merge upstream/main
echo "🔄 Merging upstream/main..."
git merge upstream/main

# Push 到 origin
echo "📤 Push 到 origin..."
git push origin $BRANCH

# 檢查是否有 package.json 變更
if git diff HEAD@{1} --name-only | grep -q "package.json"; then
    echo "📦 檢測到 package.json 變更，執行 npm install..."
    npm ci
    
    # 詢問是否要 build
    read -p "是否要執行 npm run build? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        npm run build
    fi
else
    echo "✅ 沒有依賴變更"
fi

echo "✨ 同步完成！"